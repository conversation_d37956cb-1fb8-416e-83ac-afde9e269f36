import { createClient } from '@supabase/supabase-js'

// This file should ONLY be imported on the server side
// It contains the service role key which should never be exposed to the client

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

// Validate environment variables
if (!supabaseUrl) {
  throw new Error('Missing NEXT_PUBLIC_SUPABASE_URL environment variable')
}
if (!supabaseServiceRoleKey) {
  throw new Error('Missing SUPABASE_SERVICE_ROLE_KEY environment variable')
}

// Admin client with service role privileges (SERVER ONLY)
export const supabaseAdmin = createClient(supabaseUrl, supabaseServiceRoleKey)

// Admin query helpers (for server-side operations only)
export const supabaseAdminQuery = {
  // Departments
  departments: () => supabaseAdmin.from('appy_departments'),
  
  // Employees  
  employees: () => supabaseAdmin.from('appy_employees'),
  
  // Managers
  managers: () => supabaseAdmin.from('appy_managers'),
  
  // Appraisal periods
  appraisalPeriods: () => supabaseAdmin.from('appy_appraisal_periods'),
  
  // Appraisals
  appraisals: () => supabaseAdmin.from('appy_appraisals'),
  
  // Employee managers
  employeeManagers: () => supabaseAdmin.from('appy_employee_managers'),
  
  // Employee KPIs
  employeeKpis: () => supabaseAdmin.from('appy_employee_kpis'),

  // Employee Feedback
  employeeFeedback: () => supabaseAdmin.from('appy_employee_feedback'),
  feedbackComments: () => supabaseAdmin.from('appy_feedback_comments'),
  feedbackStatusHistory: () => supabaseAdmin.from('appy_feedback_status_history'),
}

console.log('🔑 Supabase admin client initialized (SERVER ONLY)')